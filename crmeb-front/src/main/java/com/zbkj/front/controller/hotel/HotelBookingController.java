package com.zbkj.front.controller.hotel;

import com.github.pagehelper.PageInfo;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelRoomListRequest;
import com.zbkj.common.request.hotel.HotelSearchRequest;
import com.zbkj.common.response.hotel.HotelListResponse;
import com.zbkj.common.response.hotel.HotelRoomListResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.hotel.HotelBookingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 酒店预订控制器 - 用户端
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("api/front/hotel")
@Api(tags = "用户端 - 酒店预订")
@Validated
public class HotelBookingController {

    @Autowired
    private HotelBookingService hotelBookingService;

    /**
     * 酒店列表查询
     */
    @ApiOperation(value = "酒店列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<HotelListResponse>> getHotelList(
            @ModelAttribute @Validated HotelSearchRequest request,
            @ModelAttribute @Validated PageParamRequest pageParamRequest) {
        
        PageInfo<HotelListResponse> pageInfo = hotelBookingService.getHotelList(request, pageParamRequest);
        return CommonResult.success(CommonPage.restPage(pageInfo));
    }

    /**
     * 酒店详情
     */
    @ApiOperation(value = "酒店详情")
    @RequestMapping(value = "/detail/{hotelId}", method = RequestMethod.GET)
    public CommonResult<HotelListResponse> getHotelDetail(
            @PathVariable @NotNull(message = "酒店ID不能为空") Integer hotelId) {
        
        HotelListResponse hotel = hotelBookingService.getHotelDetail(hotelId);
        return CommonResult.success(hotel);
    }

    /**
     * 房型列表查询
     */
    @ApiOperation(value = "房型列表查询")
    @RequestMapping(value = "/room/list", method = RequestMethod.GET)
    public CommonResult<List<HotelRoomListResponse>> getRoomList(
            @ModelAttribute @Validated HotelRoomListRequest request) {
        
        List<HotelRoomListResponse> roomList = hotelBookingService.getRoomList(request);
        return CommonResult.success(roomList);
    }

    /**
     * 解析商品名称（调试用）
     */
    @ApiOperation(value = "解析商品名称")
    @RequestMapping(value = "/parse/product", method = RequestMethod.GET)
    public CommonResult<HotelBookingService.HotelProductInfo> parseProductName(
            @RequestParam @NotNull(message = "商品名称不能为空") String productName) {
        
        HotelBookingService.HotelProductInfo info = hotelBookingService.parseProductName(productName);
        return CommonResult.success(info);
    }
}
