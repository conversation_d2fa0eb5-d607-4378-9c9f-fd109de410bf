package com.zbkj.front.controller.hotel;

import com.github.pagehelper.PageInfo;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelRoomListRequest;
import com.zbkj.common.request.hotel.HotelSearchRequest;
import com.zbkj.common.response.hotel.HotelListResponse;
import com.zbkj.common.response.hotel.HotelRoomListResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.hotel.HotelBookingService;
import com.zbkj.service.service.hotel.HotelCancelRuleService;
import com.zbkj.service.service.OrderService;
import com.zbkj.common.model.order.Order;
import com.zbkj.common.model.order.OrderDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 酒店预订控制器 - 用户端
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("api/front/hotel")
@Api(tags = "用户端 - 酒店预订")
@Validated
public class HotelBookingController {

    @Autowired
    private HotelBookingService hotelBookingService;

    @Autowired
    private HotelCancelRuleService hotelCancelRuleService;

    @Autowired
    private OrderService orderService;

    /**
     * 酒店列表查询
     */
    @ApiOperation(value = "酒店列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<HotelListResponse>> getHotelList(
            @ModelAttribute @Validated HotelSearchRequest request,
            @ModelAttribute @Validated PageParamRequest pageParamRequest) {
        
        PageInfo<HotelListResponse> pageInfo = hotelBookingService.getHotelList(request, pageParamRequest);
        return CommonResult.success(CommonPage.restPage(pageInfo));
    }

    /**
     * 酒店详情
     */
    @ApiOperation(value = "酒店详情")
    @RequestMapping(value = "/detail/{hotelId}", method = RequestMethod.GET)
    public CommonResult<HotelListResponse> getHotelDetail(
            @PathVariable @NotNull(message = "酒店ID不能为空") Integer hotelId) {
        
        HotelListResponse hotel = hotelBookingService.getHotelDetail(hotelId);
        return CommonResult.success(hotel);
    }

    /**
     * 房型列表查询
     */
    @ApiOperation(value = "房型列表查询")
    @RequestMapping(value = "/room/list", method = RequestMethod.GET)
    public CommonResult<List<HotelRoomListResponse>> getRoomList(
            @ModelAttribute @Validated HotelRoomListRequest request) {
        
        List<HotelRoomListResponse> roomList = hotelBookingService.getRoomList(request);
        return CommonResult.success(roomList);
    }

    /**
     * 解析商品名称（调试用）
     */
    @ApiOperation(value = "解析商品名称")
    @RequestMapping(value = "/parse/product", method = RequestMethod.GET)
    public CommonResult<HotelBookingService.HotelProductInfo> parseProductName(
            @RequestParam @NotNull(message = "商品名称不能为空") String productName) {

        HotelBookingService.HotelProductInfo info = hotelBookingService.parseProductName(productName);
        return CommonResult.success(info);
    }

    /**
     * 计算酒店退款金额
     * 根据酒店取消规则和提前取消时间计算退款金额
     */
    @ApiOperation(value = "计算酒店退款金额")
    @RequestMapping(value = "/calculate-refund", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> calculateHotelRefundAmount(
            @RequestParam @NotNull(message = "订单号不能为空") String orderNo,
            @RequestParam @NotNull(message = "订单详情ID不能为空") Integer orderDetailId) {

        try {
            // 获取订单信息
            Order order = orderService.getByOrderNo(orderNo);
            if (order == null) {
                return CommonResult.failed("订单不存在");
            }

            // 获取订单详情
            OrderDetail orderDetail = orderService.getOrderDetailById(orderDetailId);
            if (orderDetail == null) {
                return CommonResult.failed("订单详情不存在");
            }

            // 检查是否为酒店商品(type=7)
            if (!Integer.valueOf(7).equals(orderDetail.getProductType())) {
                return CommonResult.failed("非酒店商品不支持此退款计算");
            }

            // 解析商品名称获取入住日期
            HotelBookingService.HotelProductInfo productInfo = hotelBookingService.parseProductName(orderDetail.getProductName());
            if (productInfo == null || productInfo.getCheckInDate() == null) {
                return CommonResult.failed("无法解析酒店商品信息");
            }

            // 计算提前取消小时数
            LocalDateTime checkInDateTime;
            try {
                // 假设入住时间为当天14:00
                checkInDateTime = LocalDateTime.parse(productInfo.getCheckInDate() + "T14:00:00");
            } catch (Exception e) {
                return CommonResult.failed("入住日期格式错误");
            }

            LocalDateTime now = LocalDateTime.now();
            long advanceHours = ChronoUnit.HOURS.between(now, checkInDateTime);

            // 如果已经过了入住时间，不允许退款
            if (advanceHours < 0) {
                Map<String, Object> result = new HashMap<>();
                result.put("canCancel", false);
                result.put("refundAmount", BigDecimal.ZERO);
                result.put("cancelFee", orderDetail.getPrice());
                result.put("message", "已过入住时间，无法申请退款");
                return CommonResult.success(result);
            }

            // 计算取消费用
            BigDecimal cancelFee = hotelCancelRuleService.calculateCancelFee(
                orderDetail.getPrice(),
                (int) advanceHours,
                order.getMerId()
            );

            Map<String, Object> result = new HashMap<>();

            // 检查是否不可取消(-1表示不可取消)
            if (cancelFee.compareTo(new BigDecimal("-1")) == 0) {
                result.put("canCancel", false);
                result.put("refundAmount", BigDecimal.ZERO);
                result.put("cancelFee", orderDetail.getPrice());
                result.put("message", "根据取消规则，当前时间不允许取消订单");
            } else {
                // 计算退款金额 = 订单金额 - 取消费用
                BigDecimal refundAmount = orderDetail.getPrice().subtract(cancelFee);
                result.put("canCancel", true);
                result.put("refundAmount", refundAmount);
                result.put("cancelFee", cancelFee);
                result.put("advanceHours", advanceHours);
                result.put("checkInDate", productInfo.getCheckInDate());

                if (cancelFee.compareTo(BigDecimal.ZERO) == 0) {
                    result.put("message", "免费取消");
                } else {
                    result.put("message", "扣除取消费用：¥" + cancelFee);
                }
            }

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("计算酒店退款金额失败", e);
            return CommonResult.failed("计算退款金额失败：" + e.getMessage());
        }
    }
}
