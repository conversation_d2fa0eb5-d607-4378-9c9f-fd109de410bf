<template>
  <view class="order-detail-page">
    <view v-if="orderDetail" class="order-content">
      <!-- 订单状态卡片 -->
      <view class="status-card">
        <view class="status-icon">
          <u-icon :name="getStatusIcon(orderDetail.status)" size="60" :color="getStatusColor(orderDetail.status)"></u-icon>
        </view>
        <view class="status-info">
          <view class="status-text">{{ getStatusText(orderDetail.status, orderDetail.refundStatus) }}</view>
          <view class="status-desc">{{ getStatusDesc(orderDetail.status) }}</view>
        </view>
      </view>

      <!-- 核销码卡片 (仅待核销状态显示) -->
      <view v-if="orderDetail.status === 3 && verifyCode" class="verify-card">
        <view class="verify-header">
          <u-icon name="qrcode" size="40" color="#007aff"></u-icon>
          <text class="verify-title">核销码</text>
        </view>
        <view class="verify-code">{{ verifyCode }}</view>
        <view class="verify-tip">请向商家出示此核销码完成核销</view>
      </view>

      <!-- 商户信息 -->
      <view class="merchant-card" v-for="merchantOrder in orderDetail.merchantOrderList" :key="merchantOrder.merId">
        <view class="card-header">
          <view class="merchant-name">{{ merchantOrder.merName }}</view>
          <view class="shipping-type">{{ getShippingTypeText(merchantOrder.shippingType) }}</view>
        </view>

        <!-- 商品列表 -->
        <view class="product-list">
          <view class="product-item" v-for="product in merchantOrder.orderInfoList" :key="product.id">
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-info">
              <view class="product-name">{{ product.productName }}</view>
              <view class="product-spec">{{ product.sku }}</view>
              <view class="product-price">
                <text class="price">¥{{ product.price }}</text>
                <text class="quantity">x{{ product.payNum }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="order-info-card">
        <view class="card-title">订单信息</view>
        <view class="info-list">
          <view class="info-item">
            <text class="label">订单号</text>
            <text class="value">{{ orderDetail.orderNo }}</text>
          </view>
          <view class="info-item">
            <text class="label">创建时间</text>
            <text class="value">{{ formatTime(orderDetail.createTime) }}</text>
          </view>
          <view class="info-item" v-if="orderDetail.payTime">
            <text class="label">支付时间</text>
            <text class="value">{{ formatTime(orderDetail.payTime) }}</text>
          </view>
          <view class="info-item">
            <text class="label">支付方式</text>
            <text class="value">{{ getPayTypeText(orderDetail.payType) }}</text>
          </view>
        </view>
      </view>

      <!-- 费用明细 -->
      <view class="price-detail-card">
        <view class="card-title">费用明细</view>
        <view class="price-list">
          <view class="price-item">
            <text class="label">商品总价</text>
            <text class="value">¥{{ orderDetail.proTotalPrice }}</text>
          </view>
          <view class="price-item" v-if="orderDetail.totalPostage > 0">
            <text class="label">运费</text>
            <text class="value">¥{{ orderDetail.totalPostage }}</text>
          </view>
          <view class="price-item" v-if="orderDetail.couponPrice > 0">
            <text class="label">优惠券</text>
            <text class="value">-¥{{ orderDetail.couponPrice }}</text>
          </view>
          <view class="price-item" v-if="orderDetail.integralPrice > 0">
            <text class="label">积分抵扣</text>
            <text class="value">-¥{{ orderDetail.integralPrice }}</text>
          </view>
          <view class="price-item total">
            <text class="label">实付金额</text>
            <text class="value total-price">¥{{ orderDetail.payPrice }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions" v-if="orderDetail">
      <!-- 待支付状态 -->
      <template v-if="orderDetail.status === 0 && !orderDetail.paid">
        <u-button type="default" @click="cancelOrder">取消订单</u-button>
        <u-button type="primary" @click="goToPay">立即支付</u-button>
      </template>
      
      <!-- 待核销状态 -->
      <template v-if="orderDetail.status === 3">
        <u-button type="primary" @click="showVerifyCodeModal">查看核销码</u-button>
      </template>
      
      <!-- 待收货状态 -->
      <template v-if="orderDetail.status === 4">
        <u-button type="primary" @click="confirmReceive">确认收货</u-button>
      </template>
      
      <!-- 已完成状态 -->
      <template v-if="orderDetail.status === 6">
        <u-button type="default" @click="deleteOrder">删除订单</u-button>
      </template>
    </view>

    <!-- 核销码弹窗 -->
    <u-popup
      :show="verifyCodeModal"
      mode="center"
      border-radius="20"
      @close="verifyCodeModal = false"
    >
      <view class="verify-popup-content">
        <!-- 头部 -->
        <view class="verify-header">
          <view class="verify-title">核销码</view>
          <u-icon name="close" size="24" color="#999" @click="verifyCodeModal = false"></u-icon>
        </view>

        <!-- 核销码主体 -->
        <view class="verify-main">
          <!-- 二维码区域 -->
          <view class="qr-section">
            <view class="qr-container">
              <qr-code
                v-if="verifyCode && verifyCode !== '暂无核销码'"
                :text="verifyCode"
                :size="180"
                canvas-id="detail-verify-qrcode"
                @success="onQRCodeSuccess"
                @error="onQRCodeError"
              ></qr-code>
              <view v-else class="qr-placeholder">
                <view class="placeholder-box">
                  <u-icon name="qrcode" size="60" color="#ccc"></u-icon>
                  <text class="placeholder-text">暂无核销码</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 核销码信息 -->
          <view class="code-info">
            <view class="code-title">核销码</view>
            <view class="code-value">{{ verifyCode || '暂无核销码' }}</view>
            <view class="code-actions">
              <view class="action-btn" @click="copyVerifyCode">
                <u-icon name="copy" size="16" color="#007aff"></u-icon>
                <text>复制</text>
              </view>
            </view>
          </view>

          <!-- 提示信息 -->
          <view class="tips">
            <text class="tip-text">请向商家出示此核销码完成核销</text>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="verify-footer">
          <u-button type="primary" text="我知道了" @click="verifyCodeModal = false"></u-button>
        </view>
      </view>
    </u-popup>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading mode="circle"></u-loading>
    </view>
  </view>
</template>

<script>
import { getOrderDetail, cancelOrder as cancelOrderApi, deleteOrder as deleteOrderApi, confirmReceive } from '@/nxTemp/apis/shopping.js'
import QrCode from '@/components/qr-code/qr-code.vue'

export default {
  name: 'OrderDetail',
  components: {
    QrCode
  },
  data() {
    return {
      orderNo: '',
      orderDetail: null,
      loading: true,
      verifyCodeModal: false,
      verifyCode: '' // 核销码
    }
  },
  onLoad(options) {
    if (options.orderNo) {
      this.orderNo = options.orderNo
      this.getOrderDetail()
    }
  },
  methods: {
    // 获取订单详情
    async getOrderDetail() {
      try {
        this.loading = true
        const res = await getOrderDetail(this.orderNo)
        
        // 处理API响应数据
        let orderData = null
        if (res.data && res.data.code === 200) {
          orderData = res.data.data
        } else if (res.data) {
          orderData = res.data
        } else {
          orderData = res
        }
        
        if (!orderData) {
          throw new Error('订单数据为空')
        }
        
        this.orderDetail = orderData
        
        // 提取核销码
        if (orderData.merchantOrderList && orderData.merchantOrderList.length > 0) {
          const merchantOrder = orderData.merchantOrderList[0]
          this.verifyCode = merchantOrder.verifyCode || ''
        } else if (orderData.verifyCode) {
          this.verifyCode = orderData.verifyCode
        }
        
        console.log('订单详情:', orderData)
        console.log('核销码:', this.verifyCode)
      } catch (err) {
        console.error('获取订单详情失败:', err)
        this.$u.toast('获取订单详情失败')
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } finally {
        this.loading = false
      }
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        0: 'clock',
        1: 'car',
        3: 'checkmark-circle',
        4: 'car',
        6: 'checkmark-circle-fill',
        9: 'close-circle'
      }
      return iconMap[status] || 'help-circle'
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        0: '#ff9500',
        1: '#007aff',
        3: '#34c759',
        4: '#007aff',
        6: '#34c759',
        9: '#ff3b30'
      }
      return colorMap[status] || '#8e8e93'
    },

    // 获取状态文本
    getStatusText(status, refundStatus) {
      if (refundStatus === 3) return '已退款'
      
      const statusMap = {
        0: '待支付',
        1: '待发货',
        3: '待核销',
        4: '待收货',
        6: '已完成',
        9: '已取消'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取状态描述
    getStatusDesc(status) {
      const descMap = {
        0: '请尽快完成支付',
        1: '商家正在准备发货',
        3: '请到店核销',
        4: '商品正在配送中',
        6: '订单已完成',
        9: '订单已取消'
      }
      return descMap[status] || ''
    },

    // 获取配送方式文本
    getShippingTypeText(shippingType) {
      const typeMap = {
        1: '快递配送',
        2: '到店自提',
        3: '无需配送'
      }
      return typeMap[shippingType] || '配送方式'
    },

    // 获取支付方式文本
    getPayTypeText(payType) {
      const typeMap = {
        'weixin': '微信支付',
        'alipay': '支付宝',
        'yue': '余额支付'
      }
      return typeMap[payType] || payType || '未支付'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN')
    },

    // 显示核销码弹窗
    showVerifyCodeModal() {
      if (!this.verifyCode) {
        this.$u.toast('暂无核销码')
        return
      }
      this.verifyCodeModal = true
    },

    // 跳转到支付页面
    goToPay() {
      uni.navigateTo({
        url: `/pages/payment/index?orderNo=${this.orderNo}`
      })
    },

    // 取消订单
    async cancelOrder() {
      try {
        const confirm = await this.$u.modal.confirm('确定要取消此订单吗？')
        if (confirm) {
          const res = await cancelOrderApi(this.orderNo)
          if (res.data && res.data.code === 200) {
            this.$u.toast('订单已取消')
            this.getOrderDetail() // 刷新订单详情
          } else {
            this.$u.toast(res.data?.message || '取消订单失败')
          }
        }
      } catch (err) {
        console.error('取消订单失败:', err)
        this.$u.toast('取消订单失败')
      }
    },

    // 删除订单
    async deleteOrder() {
      try {
        const confirm = await this.$u.modal.confirm('确定要删除此订单吗？')
        if (confirm) {
          const res = await deleteOrderApi(this.orderNo)
          if (res.data && res.data.code === 200) {
            this.$u.toast('订单已删除')
            uni.navigateBack()
          } else {
            this.$u.toast(res.data?.message || '删除订单失败')
          }
        }
      } catch (err) {
        console.error('删除订单失败:', err)
        this.$u.toast('删除订单失败')
      }
    },

    // 确认收货
    async confirmReceive() {
      try {
        const confirm = await this.$u.modal.confirm('确定已收到商品吗？')
        if (confirm) {
          const res = await confirmReceive(this.orderNo)
          if (res.data && res.data.code === 200) {
            this.$u.toast('确认收货成功')
            this.getOrderDetail() // 刷新订单详情
          } else {
            this.$u.toast(res.data?.message || '确认收货失败')
          }
        }
      } catch (err) {
        console.error('确认收货失败:', err)
        this.$u.toast('确认收货失败')
      }
    },

    // 二维码生成成功回调
    onQRCodeSuccess(data) {
      console.log('二维码生成成功:', data)
    },

    // 二维码生成失败回调
    onQRCodeError(error) {
      console.error('二维码生成失败:', error)
    },

    // 复制核销码
    copyVerifyCode() {
      if (!this.verifyCode || this.verifyCode === '暂无核销码') {
        this.$u.toast('暂无可复制的核销码')
        return
      }

      uni.setClipboardData({
        data: this.verifyCode,
        success: () => {
          this.$u.toast('核销码已复制到剪贴板')
        },
        fail: () => {
          this.$u.toast('复制失败')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.order-detail-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.status-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;

  .status-icon {
    margin-right: 24rpx;
  }

  .status-info {
    flex: 1;

    .status-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
    }

    .status-desc {
      font-size: 24rpx;
      color: #8e8e93;
    }
  }
}

.verify-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;

  .verify-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24rpx;

    .verify-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-left: 16rpx;
    }
  }

  .verify-code {
    font-size: 48rpx;
    font-weight: bold;
    color: #007aff;
    margin-bottom: 16rpx;
    letter-spacing: 4rpx;
  }

  .verify-tip {
    font-size: 24rpx;
    color: #8e8e93;
  }
}

.merchant-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .merchant-name {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }

    .shipping-type {
      font-size: 24rpx;
      color: #8e8e93;
    }
  }
}

.product-list {
  .product-item {
    display: flex;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      margin-right: 24rpx;
    }

    .product-info {
      flex: 1;

      .product-name {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
        line-height: 1.4;
      }

      .product-spec {
        font-size: 24rpx;
        color: #8e8e93;
        margin-bottom: 16rpx;
      }

      .product-price {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .price {
          font-size: 28rpx;
          color: #ff3b30;
          font-weight: bold;
        }

        .quantity {
          font-size: 24rpx;
          color: #8e8e93;
        }
      }
    }
  }
}

.order-info-card, .price-detail-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;

  .card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.info-list, .price-list {
  .info-item, .price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    &.total {
      border-top: 1rpx solid #f0f0f0;
      padding-top: 16rpx;
      margin-top: 16rpx;

      .total-price {
        font-size: 32rpx;
        font-weight: bold;
        color: #ff3b30;
      }
    }

    .label {
      font-size: 28rpx;
      color: #8e8e93;
    }

    .value {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 24rpx;

  .u-button {
    flex: 1;
  }
}

.verify-modal-content {
  text-align: center;
  padding: 40rpx 0;

  .verify-code-large {
    font-size: 64rpx;
    font-weight: bold;
    color: #007aff;
    margin-bottom: 24rpx;
    letter-spacing: 6rpx;
  }

  .verify-tip-modal {
    font-size: 28rpx;
    color: #8e8e93;
  }
}

.loading-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 核销码弹窗样式 */
.verify-popup-content {
  width: 640rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 20rpx;
}

.verify-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .verify-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.verify-main {
  padding: 32rpx;
  text-align: center;
}

.qr-section {
  margin-bottom: 32rpx;

  .qr-container {
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto;
    background-color: #fff;
    border: 2rpx solid #f0f0f0;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx;
    position: relative;
    z-index: 1;

    .qr-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .placeholder-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .placeholder-text {
          font-size: 22rpx;
          color: #999;
          margin-top: 12rpx;
        }
      }
    }
  }
}

.code-info {
  margin-bottom: 32rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  position: relative;
  z-index: 2;

  .code-title {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
    text-align: center;
  }

  .code-value {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    letter-spacing: 3rpx;
    font-family: 'Courier New', 'Monaco', monospace;
    margin-bottom: 16rpx;
    word-break: break-all;
    text-align: center;
    line-height: 1.4;
  }

  .code-actions {
    text-align: center;

    .action-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx 20rpx;
      background-color: #fff;
      border-radius: 16rpx;
      font-size: 22rpx;
      color: #007aff;
      border: 1rpx solid #e0e0e0;

      text {
        margin-left: 6rpx;
      }
    }
  }
}

.tips {
  .tip-text {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }
}

.verify-footer {
  padding: 0 32rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 24rpx;
  padding-top: 24rpx;

  .u-button {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
  }
}
</style>
